use mysql::{
    Opts<PERSON><PERSON><PERSON>,
    Pool,
    prelude::Queryable,
};

use crate::utils_classes::{
    MYSQLConfig,
    MYSQLValue,
};

use crate::rahavard::{
    convert_byte,
};


pub fn get_size_of_database(database_name: &str, convert: bool) -> Result<String, Box<dyn std::error::Error>> {
    let mysql_host = match MYSQLConfig::MYSQL_HOST.value() {
        MYSQLValue::Str(host) => host,
        _ => return Err("Error getting MYSQL_HOST".into()),
    };
    let mysql_user = match MYSQLConfig::MYSQL_R_USER.value() {
        MYSQLValue::Str(user) => user,
        _ => return Err("Error getting MYSQL_R_USER".into()),
    };
    let mysql_password = match MYSQLConfig::MYSQL_R_USER_PASSWD.value() {
        MYSQLValue::Str(password) => password,
        _ => return Err("Error getting MYSQL_R_USER_PASSWD".into()),
    };

    let db_opts = OptsBuilder::new()
        .ip_or_hostname(Some(mysql_host))
        .user(Some(mysql_user))
        .pass(Some(mysql_password));

    let pool = Pool::new(db_opts)?;
    let mut conn = pool.get_conn()?;

    let query = "
        SELECT SUM((DATA_LENGTH + INDEX_LENGTH))
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = ?
    ";

    let result: Option<u64> = conn.exec_first(query, (database_name,))?;
    let size_bytes = result.unwrap_or(0);

    if convert {
        Ok(convert_byte(size_bytes as f64))
    } else {
        Ok(size_bytes.to_string())
    }
}


/// Get list of table names in a database
///
/// # Arguments
/// * `database_name` - The name of the database to get tables from
///
/// # Returns
/// * A vector of table names as strings
/// * Example: vec!["snorttable", "sourceiptoptable", ...]
pub fn get_tables(database_name: &str) -> Result<Vec<String>, Box<dyn std::error::Error>> {
    // Get R_USER credentials
    let mysql_host = match MYSQLConfig::MYSQL_HOST.value() {
        MYSQLValue::Str(host) => host,
        _ => return Err("Error getting MYSQL_HOST".into()),
    };
    let mysql_user = match MYSQLConfig::MYSQL_R_USER.value() {
        MYSQLValue::Str(user) => user,
        _ => return Err("Error getting MYSQL_R_USER".into()),
    };
    let mysql_password = match MYSQLConfig::MYSQL_R_USER_PASSWD.value() {
        MYSQLValue::Str(password) => password,
        _ => return Err("Error getting MYSQL_R_USER_PASSWD".into()),
    };

    // Create database connection options
    let db_opts = OptsBuilder::new()
        .ip_or_hostname(Some(mysql_host))
        .user(Some(mysql_user))
        .pass(Some(mysql_password));

    // Connect to database and execute query
    let pool = Pool::new(db_opts)?;
    let mut conn = pool.get_conn()?;

    let query = "
        SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = ?
    ";

    let table_names: Vec<String> = conn.exec(query, (database_name,))?;

    Ok(table_names)
}

// get_tables_and_sizes
// get_size_of_table

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_get_tables_functionality() {
        // This test verifies the function compiles and has the correct signature
        // Note: This won't actually connect to a database in tests

        // Test that the function signature is correct
        let _result: Result<Vec<String>, Box<dyn std::error::Error>> =
            get_tables("test_db");

        // The function should return an error in test environment since no DB is available
        // but this confirms the types are correct
    }

    #[test]
    fn test_get_size_of_database_functionality() {
        // This test verifies the function compiles and has the correct signature
        // Note: This won't actually connect to a database in tests

        // Test that the function signature is correct
        let _result: Result<String, Box<dyn std::error::Error>> =
            get_size_of_database("test_db", false);

        // The function should return an error in test environment since no DB is available
        // but this confirms the types are correct
    }
}
